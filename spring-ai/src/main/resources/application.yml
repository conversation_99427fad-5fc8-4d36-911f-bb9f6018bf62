server:
  port: 8080
spring:
  ai:
    openai:
      api-key: ********************************************************************************************************************************************************************
      base-url: https://api.openai.com
      chat:
        options:
          model: gpt-4o-mini
    chat:
      client:
        enabled: false # 禁用SpringAI自动配置的ChatClient，这样就可以手动创建多个 ChatClient 实例。
logging:
  level:
    org:
      springframework:
        ai:
          chat:
            client:
              advisor: DEBUG # 查看SimpleLoggerAdvisor的日志