package cn.sunxiansheng.springai.controller;

import jakarta.annotation.Resource;
import lombok.Data;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.converter.ListOutputConverter;
import org.springframework.ai.converter.MapOutputConverter;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Description: StructuredOutputController
 *
 * <AUTHOR>
 * @Create 2025/7/11 10:52
 * @Version 1.0
 */
@RestController
@RequestMapping("/weather")
public class StructuredOutputController {

    @Resource
    private OpenAiChatModel openAiChatModel;

    @GetMapping("/get")
    public ActorsFilms getActorsFilms(@RequestParam("prompt") String prompt) {
        // 输出结构定义
        BeanOutputConverter<ActorsFilms> beanOutputConverter = new BeanOutputConverter<>(ActorsFilms.class);
        String format = beanOutputConverter.getFormat();

        // 拼接用户提示 + 输出格式要求（提示 AI 按照格式返回）
        String fullPrompt = prompt + "\n" + format;

        Prompt prompt1 = new Prompt(fullPrompt);
        Generation generation = openAiChatModel.call(prompt1).getResult();

        // 将 AI 输出转为结构化 Java Bean
        return beanOutputConverter.convert(generation.getOutput().getText());
    }

    @Data
    public static class ActorsFilms {
        private String actor;
        private List<String> movies;
    }

    @GetMapping("/getMap")
    public Map<String, Object> getMap(@RequestParam("prompt") String prompt) {
        // 输出结构定义
        MapOutputConverter mapOutputConverter = new MapOutputConverter();
        String format = mapOutputConverter.getFormat();

        String fullPrompt = prompt + "\n" + format;

        Prompt prompt1 = new Prompt(fullPrompt);
        Generation generation = openAiChatModel.call(prompt1).getResult();
        Map<String, Object> convert = mapOutputConverter.convert(generation.getOutput().getText());

        return convert;
    }

    @GetMapping("/getList")
    public List<String> getList(@RequestParam("prompt") String prompt) {
        // 输出结构定义
        ListOutputConverter listOutputConverter = new ListOutputConverter(new DefaultConversionService());
        String format = listOutputConverter.getFormat();

        String fullPrompt = prompt + "\n" + format;

        Prompt prompt1 = new Prompt(fullPrompt);
        Generation generation = openAiChatModel.call(prompt1).getResult();
        List<String> convert = listOutputConverter.convert(generation.getOutput().getText());

        return convert;
    }

}
