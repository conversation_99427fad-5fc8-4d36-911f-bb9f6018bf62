package cn.sunxiansheng.springai.controller;

import jakarta.annotation.Resource;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: TestController
 *
 * <AUTHOR>
 * @Create 2025/5/22 17:25
 * @Version 1.0
 */
@RestController
public class TestController {

    /**
     * 获取OpenAIChatClient
     */
    @Resource
    private ChatClient openaiChatClient;

    /**
     * A test endpoint.
     *
     * @return A sample response.
     */
    @RequestMapping("/test")
    public String test() {
        return "This is a test response from TestController";
    }

    @RequestMapping("/ask")
    public String ask() {
        return openaiChatClient.prompt("你好").call().content();
    }
}
