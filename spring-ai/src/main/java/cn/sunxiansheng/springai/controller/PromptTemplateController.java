package cn.sunxiansheng.springai.controller;

import jakarta.annotation.Resource;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.template.st.StTemplateRenderer;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Description: PromptTemplateController
 *
 * <AUTHOR>
 * @Create 2025/8/18 17:11
 * @Version 1.0
 */
@RestController
public class PromptTemplateController {

    @Resource
    private OpenAiChatModel openAiChatModel;

    /**
     * 经典例子：翻译接口
     *
     * @param text     要翻译的内容
     * @param language 目标语言
     */
    @GetMapping("/translate")
    public String translate(@RequestParam String text,
                            @RequestParam(defaultValue = "English") String language) {

        Prompt prompt = new PromptTemplate("Translate the following text into {language}:\n\n{text}")
                .create(Map.of("language", language, "text", text));

        // 调用模型
        return openAiChatModel.call(prompt).getResult().getOutput().getText();
    }

    public static void main(String[] args) {
        String prompt = PromptTemplate.builder()
                .renderer(
                        StTemplateRenderer.builder()
                                .startDelimiterToken('<')
                                .endDelimiterToken('>')
                                .build()
                )
                .template("Tell me the names of 5 movies whose soundtrack was composed by <composer>.")
                .build()   // 得到 PromptTemplate
                .render(Map.of("composer", "John Williams"));  // 立刻渲染

        System.out.println(prompt);
    }

}
