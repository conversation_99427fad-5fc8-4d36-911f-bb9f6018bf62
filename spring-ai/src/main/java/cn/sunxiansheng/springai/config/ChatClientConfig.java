package cn.sunxiansheng.springai.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Description: ChatClient
 *
 * <AUTHOR>
 * @Create 2025/5/22 17:37
 * @Version 1.0
 */
@Configuration
public class ChatClientConfig {

    /**
     * 注入openAiChatClient
     *
     * @param chatModel
     * @return
     */
    @Bean
    public ChatClient openAiChatClient(OpenAiChatModel chatModel) {
        return ChatClient.create(chatModel);
    }
}